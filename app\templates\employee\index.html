{% extends 'base.html' %}

{% block title %}员工管理 - {{ super() }}{% endblock %}

{% block styles %}
{{ super() }}
<link rel="stylesheet" href="{{ url_for('static', filename='css/enterprise-dashboard.css') }}">
<style nonce="{{ csp_nonce }}">
    /* 员工管理仪表盘专用样式 */
    .employee-dashboard-header {
        background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-dark) 100%);
        color: white;
        border-radius: 12px;
        padding: 24px;
        margin-bottom: 24px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    .employee-dashboard-header h1 {
        margin: 0;
        font-size: 28px;
        font-weight: 700;
        margin-bottom: 8px;
    }

    .employee-dashboard-header .subtitle {
        font-size: 16px;
        opacity: 0.9;
        margin: 0;
    }

    .stats-card {
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border: 1px solid #e9ecef;
        border-radius: 12px;
        padding: 24px;
        margin-bottom: 24px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        height: 100%;
    }

    .stats-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
    }

    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--theme-primary);
    }

    .stats-card.warning::before { background: #ffc107; }
    .stats-card.success::before { background: #28a745; }
    .stats-card.danger::before { background: #dc3545; }
    .stats-card.info::before { background: #17a2b8; }
    .stats-card.secondary::before { background: #6c757d; }

    .stats-card-header {
        display: flex;
        justify-content: between;
        align-items: flex-start;
        margin-bottom: 16px;
    }

    .stats-card-title {
        font-size: 14px;
        font-weight: 600;
        color: #6c757d;
        margin: 0;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .stats-card-value {
        font-size: 32px;
        font-weight: 700;
        color: #2c5aa0;
        margin: 8px 0;
        line-height: 1;
    }

    .stats-card-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: white;
        background: var(--theme-primary);
        margin-left: auto;
    }

    .stats-card.warning .stats-card-icon { background: #ffc107; }
    .stats-card.success .stats-card-icon { background: #28a745; }
    .stats-card.danger .stats-card-icon { background: #dc3545; }
    .stats-card.info .stats-card-icon { background: #17a2b8; }
    .stats-card.secondary .stats-card-icon { background: #6c757d; }

    .stats-card-footer {
        font-size: 13px;
        color: #6c757d;
        margin-top: 12px;
    }

    .action-buttons-container {
        background: white;
        border-radius: 12px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        margin-bottom: 24px;
    }

    .action-buttons {
        display: flex;
        gap: 12px;
        flex-wrap: wrap;
    }

    .action-buttons .btn {
        border-radius: 8px;
        padding: 12px 20px;
        font-weight: 600;
        font-size: 14px;
        border: none;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .action-buttons .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .chart-card {
        background: white;
        border-radius: 12px;
        padding: 24px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        margin-bottom: 24px;
        height: 100%;
    }

    .chart-card-header {
        border-bottom: 1px solid #e9ecef;
        padding-bottom: 16px;
        margin-bottom: 20px;
    }

    .chart-card-title {
        font-size: 18px;
        font-weight: 600;
        color: #2c5aa0;
        margin: 0;
    }

    .chart-container {
        height: 280px;
        position: relative;
    }

    .alert-card {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
        margin-bottom: 24px;
    }

    .alert-card-header {
        padding: 16px 20px;
        font-weight: 600;
        font-size: 16px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }

    .alert-card-header.warning {
        background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
        color: white;
    }

    .alert-card-header.danger {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
    }

    .alert-items {
        max-height: 240px;
        overflow-y: auto;
        padding: 20px;
    }

    .alert-items ul {
        margin: 0;
        padding: 0;
        list-style: none;
    }

    .alert-items li {
        padding: 12px 0;
        border-bottom: 1px solid #e9ecef;
        font-size: 14px;
        line-height: 1.5;
    }

    .alert-items li:last-child {
        border-bottom: none;
    }

    /* 移动端优化 */
    @media (max-width: 768px) {
        .employee-dashboard-header {
            padding: 20px;
            margin-bottom: 20px;
        }

        .employee-dashboard-header h1 {
            font-size: 24px;
        }

        .stats-card {
            padding: 20px;
            margin-bottom: 16px;
        }

        .stats-card-value {
            font-size: 28px;
        }

        .action-buttons {
            flex-direction: column;
        }

        .action-buttons .btn {
            width: 100%;
            margin-bottom: 8px;
        }

        .chart-container {
            height: 220px;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- 仪表盘头部 -->
<div class="employee-dashboard-header">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h1><i class="fas fa-users mr-3"></i>员工管理仪表盘</h1>
            <p class="subtitle">
                <i class="fas fa-calendar-alt mr-2"></i>今日概览
                {% if current_user.get_current_area() %}
                <span class="ml-3"><i class="fas fa-map-marker-alt mr-2"></i>{{ current_user.get_current_area().name }}</span>
                {% endif %}
            </p>
        </div>
        <div class="col-md-4 text-right desktop-only">
            <div class="d-flex justify-content-end align-items-center">
                <span class="mr-3" style="opacity: 0.9;">
                    <i class="fas fa-user-check mr-1"></i>
                    在职员工: <strong>{{ dashboard_data.active_employees }}</strong>
                </span>
            </div>
        </div>
    </div>
</div>

<!-- 快速操作按钮 -->
<div class="action-buttons-container">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h6 class="mb-3 mb-md-0" style="color: #2c5aa0; font-weight: 600;">
                <i class="fas fa-bolt mr-2"></i>快速操作
            </h6>
        </div>
        <div class="col-md-4">
            <div class="action-buttons justify-content-md-end">
                <a href="{{ url_for('employee.add_employee') }}" class="btn btn-primary">
                    <i class="fas fa-plus mr-2"></i>添加员工
                </a>
                <a href="{{ url_for('employee.health_certificates') }}" class="btn btn-info">
                    <i class="fas fa-id-card mr-2"></i>健康证管理
                </a>
                <a href="{{ url_for('employee.daily_health_check') }}" class="btn btn-success">
                    <i class="fas fa-heartbeat mr-2"></i>健康检查
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 员工统计概览 -->
<div class="row">
    <div class="col-lg-3 col-md-6 col-12">
        <div class="stats-card">
            <div class="stats-card-header">
                <div>
                    <h6 class="stats-card-title">员工总数</h6>
                    <div class="stats-card-value">{{ dashboard_data.total_employees }}</div>
                </div>
                <div class="stats-card-icon">
                    <i class="fas fa-users"></i>
                </div>
            </div>
            <div class="stats-card-footer">
                <i class="fas fa-info-circle mr-1"></i>
                包含所有状态的员工
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-12">
        <div class="stats-card success">
            <div class="stats-card-header">
                <div>
                    <h6 class="stats-card-title">在职员工</h6>
                    <div class="stats-card-value">{{ dashboard_data.active_employees }}</div>
                </div>
                <div class="stats-card-icon">
                    <i class="fas fa-user-check"></i>
                </div>
            </div>
            <div class="stats-card-footer">
                <i class="fas fa-arrow-up mr-1 text-success"></i>
                正常工作状态
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-12">
        <div class="stats-card warning">
            <div class="stats-card-header">
                <div>
                    <h6 class="stats-card-title">休假员工</h6>
                    <div class="stats-card-value">{{ dashboard_data.on_leave_employees }}</div>
                </div>
                <div class="stats-card-icon">
                    <i class="fas fa-user-clock"></i>
                </div>
            </div>
            <div class="stats-card-footer">
                <i class="fas fa-calendar-alt mr-1"></i>
                临时休假状态
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-12">
        <div class="stats-card secondary">
            <div class="stats-card-header">
                <div>
                    <h6 class="stats-card-title">离职员工</h6>
                    <div class="stats-card-value">{{ dashboard_data.inactive_employees }}</div>
                </div>
                <div class="stats-card-icon">
                    <i class="fas fa-user-times"></i>
                </div>
            </div>
            <div class="stats-card-footer">
                <i class="fas fa-minus-circle mr-1"></i>
                已离职状态
            </div>
        </div>
    </div>
</div>

<!-- 健康证状态统计 -->
<div class="row">
    <div class="col-12 mb-3">
        <h5 style="color: #2c5aa0; font-weight: 600; margin-bottom: 20px;">
            <i class="fas fa-id-card mr-2"></i>健康证状态统计
        </h5>
    </div>
    <div class="col-lg-3 col-md-6 col-12">
        <div class="stats-card info">
            <div class="stats-card-header">
                <div>
                    <h6 class="stats-card-title">健康证有效</h6>
                    <div class="stats-card-value">{{ dashboard_data.health_cert_valid }}</div>
                </div>
                <div class="stats-card-icon">
                    <i class="fas fa-id-card"></i>
                </div>
            </div>
            <div class="stats-card-footer">
                <i class="fas fa-check-circle mr-1 text-success"></i>
                证件状态正常
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-12">
        <div class="stats-card warning">
            <div class="stats-card-header">
                <div>
                    <h6 class="stats-card-title">即将到期</h6>
                    <div class="stats-card-value">{{ dashboard_data.health_cert_expiring }}</div>
                </div>
                <div class="stats-card-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
            </div>
            <div class="stats-card-footer">
                <i class="fas fa-clock mr-1 text-warning"></i>
                需要及时续办
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-12">
        <div class="stats-card danger">
            <div class="stats-card-header">
                <div>
                    <h6 class="stats-card-title">已过期</h6>
                    <div class="stats-card-value">{{ dashboard_data.health_cert_expired }}</div>
                </div>
                <div class="stats-card-icon">
                    <i class="fas fa-calendar-times"></i>
                </div>
            </div>
            <div class="stats-card-footer">
                <i class="fas fa-times-circle mr-1 text-danger"></i>
                需要立即处理
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-12">
        <div class="stats-card secondary">
            <div class="stats-card-header">
                <div>
                    <h6 class="stats-card-title">未办理</h6>
                    <div class="stats-card-value">{{ dashboard_data.health_cert_none }}</div>
                </div>
                <div class="stats-card-icon">
                    <i class="fas fa-file-alt"></i>
                </div>
            </div>
            <div class="stats-card-footer">
                <i class="fas fa-plus-circle mr-1"></i>
                需要办理证件
            </div>
        </div>
    </div>
</div>

<!-- 数据分析图表 -->
<div class="row">
    <div class="col-12 mb-3">
        <h5 style="color: #2c5aa0; font-weight: 600; margin-bottom: 20px;">
            <i class="fas fa-chart-bar mr-2"></i>数据分析
        </h5>
    </div>
    <div class="col-lg-6 col-12">
        <div class="chart-card">
            <div class="chart-card-header">
                <h6 class="chart-card-title">
                    <i class="fas fa-building mr-2"></i>部门分布
                </h6>
            </div>
            <div class="chart-container">
                <canvas id="departmentChart"></canvas>
            </div>
        </div>
    </div>
    <div class="col-lg-6 col-12">
        <div class="chart-card">
            <div class="chart-card-header">
                <h6 class="chart-card-title">
                    <i class="fas fa-user-tie mr-2"></i>职位分布
                </h6>
            </div>
            <div class="chart-container">
                <canvas id="positionChart"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-6 col-12">
        <div class="chart-card">
            <div class="chart-card-header">
                <h6 class="chart-card-title">
                    <i class="fas fa-map-marker-alt mr-2"></i>区域分布
                </h6>
            </div>
            <div class="chart-container">
                <canvas id="areaChart"></canvas>
            </div>
        </div>
    </div>
    <div class="col-lg-6 col-12">
        <div class="chart-card">
            <div class="chart-card-header">
                <h6 class="chart-card-title">
                    <i class="fas fa-user-cog mr-2"></i>系统账号关联情况
                </h6>
            </div>
            <div class="chart-container">
                <canvas id="accountChart"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- 健康证提醒 -->
{% if expiring_certs or expired_certs %}
<div class="row">
    <div class="col-12 mb-3">
        <h5 style="color: #2c5aa0; font-weight: 600; margin-bottom: 20px;">
            <i class="fas fa-bell mr-2"></i>健康证提醒
        </h5>
    </div>
    {% if expiring_certs %}
    <div class="col-lg-6 col-12">
        <div class="alert-card">
            <div class="alert-card-header warning">
                <i class="fas fa-exclamation-triangle mr-2"></i>健康证即将到期提醒
            </div>
            <div class="alert-items">
                <ul>
                    {% for item in expiring_certs %}
                    <li>
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <strong style="color: #2c5aa0;">{{ item.employee.name }}</strong>
                                <div class="text-muted small mt-1">
                                    到期日期: {{ item.certificate.expire_date|format_datetime('%Y-%m-%d') }}
                                </div>
                            </div>
                            <span class="badge badge-warning">
                                {{ item.days_left }}天后到期
                            </span>
                        </div>
                    </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    </div>
    {% endif %}
    {% if expired_certs %}
    <div class="col-lg-6 col-12">
        <div class="alert-card">
            <div class="alert-card-header danger">
                <i class="fas fa-calendar-times mr-2"></i>健康证已过期提醒
            </div>
            <div class="alert-items">
                <ul>
                    {% for item in expired_certs %}
                    <li>
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <strong style="color: #2c5aa0;">{{ item.employee.name }}</strong>
                                <div class="text-muted small mt-1">
                                    过期日期: {{ item.certificate.expire_date|format_datetime('%Y-%m-%d') }}
                                </div>
                            </div>
                            <span class="badge badge-danger">
                                已过期{{ item.days_expired }}天
                            </span>
                        </div>
                    </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endif %}

<div class="card">
    <div class="card-body">
        <!-- 桌面端表格 -->
        <div class="table-responsive desktop-only">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>姓名</th>
                        <th>性别</th>
                        <th>职位</th>
                        <th>部门</th>
                        <th>所属区域</th>
                        <th>联系电话</th>
                        <th>状态</th>
                        <th>健康证状态</th>
                        <th>系统账号</th>
                        <th>食品安全</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for employee in employees.items %}
                    <tr>
                        <td>{{ employee.id }}</td>
                        <td>{{ employee.name }}</td>
                        <td>{{ employee.gender }}</td>
                        <td>{{ employee.position }}</td>
                        <td>{{ employee.department }}</td>
                        <td>
                            {% if employee.area %}
                            <span class="badge badge-info">{{ employee.area.get_level_name() }}</span>
                            {{ employee.area.name }}
                            {% else %}
                            <span class="text-muted">未设置</span>
                            {% endif %}
                        </td>
                        <td>{{ employee.phone }}</td>
                        <td>
                            {% if employee.status == 1 %}
                            <span class="badge badge-success">在职</span>
                            {% elif employee.status == 0 %}
                            <span class="badge badge-secondary">离职</span>
                            {% elif employee.status == 2 %}
                            <span class="badge badge-warning">休假</span>
                            {% endif %}
                        </td>
                        <td>
                            {% set cert_status = employee.get_health_certificate_status() %}
                            {% if cert_status == "有效" %}
                            <span class="badge badge-success">{{ cert_status }}</span>
                            {% elif cert_status == "未办理" %}
                            <span class="badge badge-secondary">{{ cert_status }}</span>
                            {% elif cert_status == "已过期" %}
                            <span class="badge badge-danger">{{ cert_status }}</span>
                            {% elif "即将过期" in cert_status %}
                            <span class="badge badge-warning">{{ cert_status }}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if employee.user %}
                                <span class="badge badge-primary">已关联</span>
                                {% if employee.user.status == 0 %}
                                    <span class="badge badge-danger">已禁用</span>
                                {% endif %}
                            {% else %}
                                <span class="badge badge-secondary">未关联</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if employee.responsible_areas %}
                                <span class="badge badge-info" title="负责区域">
                                    <i class="fas fa-map-marker-alt"></i>
                                </span>
                            {% endif %}

                            {% if employee.food_safety_certifications %}
                                <span class="badge badge-success" title="食品安全证书">
                                    <i class="fas fa-certificate"></i>
                                </span>
                            {% endif %}

                            {% if employee.safety_violation_count and employee.safety_violation_count > 0 %}
                                <span class="badge badge-danger" title="安全违规: {{ employee.safety_violation_count }}次">
                                    <i class="fas fa-exclamation-triangle"></i> {{ employee.safety_violation_count }}
                                </span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('employee.view_employee', id=employee.id) }}" class="btn btn-info" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('employee.edit_employee', id=employee.id) }}" class="btn btn-primary" title="编辑信息">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{{ url_for('employee.add_health_certificate', employee_id=employee.id) }}" class="btn btn-warning" title="添加健康证">
                                    <i class="fas fa-id-card"></i>
                                </a>
                                <a href="{{ url_for('employee.add_health_check', employee_id=employee.id) }}" class="btn btn-success" title="健康检查">
                                    <i class="fas fa-heartbeat"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="12" class="text-center">暂无员工数据</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- 移动端员工卡片 -->
        <div class="mobile-only">
            {% for employee in employees.items %}
            <div class="card mb-3 border-left-{% if employee.status == 1 %}success{% elif employee.status == 2 %}warning{% else %}secondary{% endif %}">
                <div class="card-body py-2">
                    <div class="row">
                        <div class="col-8">
                            <h6 class="mb-1">{{ employee.name }}</h6>
                            <small class="text-muted">ID: {{ employee.id }} | {{ employee.gender }}</small>
                        </div>
                        <div class="col-4 text-right">
                            {% if employee.status == 1 %}
                            <span class="badge badge-success">在职</span>
                            {% elif employee.status == 0 %}
                            <span class="badge badge-secondary">离职</span>
                            {% elif employee.status == 2 %}
                            <span class="badge badge-warning">休假</span>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mt-2">
                        <div class="col-6">
                            <small class="text-muted">职位</small>
                            <div class="small">{{ employee.position }}</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">部门</small>
                            <div class="small">{{ employee.department }}</div>
                        </div>
                    </div>

                    <div class="row mt-2">
                        <div class="col-6">
                            <small class="text-muted">联系电话</small>
                            <div class="small">{{ employee.phone }}</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">所属区域</small>
                            <div class="small">
                                {% if employee.area %}
                                {{ employee.area.name }}
                                {% else %}
                                未设置
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row mt-2">
                        <div class="col-6">
                            <small class="text-muted">健康证状态</small>
                            <div>
                                {% set cert_status = employee.get_health_certificate_status() %}
                                {% if cert_status == "有效" %}
                                <span class="badge badge-success badge-sm">{{ cert_status }}</span>
                                {% elif cert_status == "未办理" %}
                                <span class="badge badge-secondary badge-sm">{{ cert_status }}</span>
                                {% elif cert_status == "已过期" %}
                                <span class="badge badge-danger badge-sm">{{ cert_status }}</span>
                                {% elif "即将过期" in cert_status %}
                                <span class="badge badge-warning badge-sm">{{ cert_status }}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">系统账号</small>
                            <div>
                                {% if employee.user %}
                                <span class="badge badge-primary badge-sm">已关联</span>
                                {% if employee.user.status == 0 %}
                                <span class="badge badge-danger badge-sm">已禁用</span>
                                {% endif %}
                                {% else %}
                                <span class="badge badge-secondary badge-sm">未关联</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row mt-2">
                        <div class="col-12">
                            <div class="btn-group btn-group-sm w-100" role="group">
                                <a href="{{ url_for('employee.view_employee', id=employee.id) }}" class="btn btn-outline-info" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('employee.edit_employee', id=employee.id) }}" class="btn btn-outline-primary" title="编辑信息">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{{ url_for('employee.add_health_certificate', employee_id=employee.id) }}" class="btn btn-outline-warning" title="添加健康证">
                                    <i class="fas fa-id-card"></i>
                                </a>
                                <a href="{{ url_for('employee.add_health_check', employee_id=employee.id) }}" class="btn btn-outline-success" title="健康检查">
                                    <i class="fas fa-heartbeat"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% else %}
            <div class="text-center py-4">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5>暂无员工数据</h5>
                <p class="text-muted">您可以添加新的员工信息</p>
            </div>
            {% endfor %}
        </div>
    </div>
    {% if employees.pages > 1 %}
    <div class="card-footer">
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center mb-0">
                <li class="page-item {% if not employees.has_prev %}disabled{% endif %}">
                    <a class="page-link" href="{{ url_for('employee.index', page=employees.prev_num) if employees.has_prev else '#' }}">
                        <i class="fas fa-chevron-left"></i> 上一页
                    </a>
                </li>
                {% for page in employees.iter_pages() %}
                    {% if page %}
                        <li class="page-item {% if page == employees.page %}active{% endif %}">
                            <a class="page-link" href="{{ url_for('employee.index', page=page) }}">{{ page }}</a>
                        </li>
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    {% endif %}
                {% endfor %}
                <li class="page-item {% if not employees.has_next %}disabled{% endif %}">
                    <a class="page-link" href="{{ url_for('employee.index', page=employees.next_num) if employees.has_next else '#' }}">
                        下一页 <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            </ul>
        </nav>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/chart-js/chart.min.js') }}"></script>
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 部门分布图表
        var departmentCtx = document.getElementById('departmentChart').getContext('2d');
        var departmentChart = new Chart(departmentCtx, {
            type: 'bar',
            data: {
                labels: [
                    {% for dept, count in dashboard_data.departments.items() %}
                    '{{ dept }}',
                    {% endfor %}
                ],
                datasets: [{
                    label: '员工数量',
                    data: [
                        {% for dept, count in dashboard_data.departments.items() %}
                        {{ count }},
                        {% endfor %}
                    ],
                    backgroundColor: [
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(153, 102, 255, 0.7)',
                        'rgba(255, 159, 64, 0.7)',
                        'rgba(255, 99, 132, 0.7)'
                    ],
                    borderColor: [
                        'rgba(54, 162, 235, 1)',
                        'rgba(75, 192, 192, 1)',
                        'rgba(153, 102, 255, 1)',
                        'rgba(255, 159, 64, 1)',
                        'rgba(255, 99, 132, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        });

        // 职位分布图表
        var positionCtx = document.getElementById('positionChart').getContext('2d');
        var positionChart = new Chart(positionCtx, {
            type: 'bar',
            data: {
                labels: [
                    {% for position, count in dashboard_data.positions.items() %}
                    '{{ position }}',
                    {% endfor %}
                ],
                datasets: [{
                    label: '员工数量',
                    data: [
                        {% for position, count in dashboard_data.positions.items() %}
                        {{ count }},
                        {% endfor %}
                    ],
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.7)',
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(255, 206, 86, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(153, 102, 255, 0.7)'
                    ],
                    borderColor: [
                        'rgba(255, 99, 132, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(75, 192, 192, 1)',
                        'rgba(153, 102, 255, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        });

        // 区域分布图表
        var areaCtx = document.getElementById('areaChart').getContext('2d');
        var areaChart = new Chart(areaCtx, {
            type: 'pie',
            data: {
                labels: [
                    {% for area, count in dashboard_data.areas.items() %}
                    '{{ area }}',
                    {% endfor %}
                ],
                datasets: [{
                    data: [
                        {% for area, count in dashboard_data.areas.items() %}
                        {{ count }},
                        {% endfor %}
                    ],
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.7)',
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(255, 206, 86, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(153, 102, 255, 0.7)'
                    ],
                    borderColor: [
                        'rgba(255, 99, 132, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(75, 192, 192, 1)',
                        'rgba(153, 102, 255, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    }
                }
            }
        });

        // 系统账号关联情况图表
        var accountCtx = document.getElementById('accountChart').getContext('2d');
        var accountChart = new Chart(accountCtx, {
            type: 'doughnut',
            data: {
                labels: ['已关联系统账号', '未关联系统账号'],
                datasets: [{
                    data: [
                        {{ dashboard_data.with_system_account }},
                        {{ dashboard_data.without_system_account }}
                    ],
                    backgroundColor: [
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(255, 99, 132, 0.7)'
                    ],
                    borderColor: [
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 99, 132, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    }
                }
            }
        });
    });
</script>
{% endblock %}